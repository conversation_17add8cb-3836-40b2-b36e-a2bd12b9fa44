/**
 * Web Speech API Service for Real-Time Speech Recognition
 * Provides true real-time transcription using browser's native speech recognition
 */

interface WebSpeechOptions {
  language?: string
  continuous?: boolean
  interimResults?: boolean
  maxAlternatives?: number
}

interface TranscriptionResult {
  transcript: string
  confidence: number
  isFinal: boolean
}

type WebSpeechEventType = 
  | 'start'
  | 'end' 
  | 'result'
  | 'error'
  | 'speechstart'
  | 'speechend'
  | 'soundstart'
  | 'soundend'

class WebSpeechService {
  private recognition: SpeechRecognition | null = null
  private isSupported: boolean = false
  private isListening: boolean = false
  private eventListeners: Map<WebSpeechEventType, Function[]> = new Map()
  private options: WebSpeechOptions

  constructor(options: WebSpeechOptions = {}) {
    this.options = {
      language: 'en-US',
      continuous: true,
      interimResults: true,
      maxAlternatives: 1,
      ...options
    }

    this.checkSupport()
    this.initializeRecognition()
  }

  /**
   * Check if Web Speech API is supported
   */
  private checkSupport(): void {
    this.isSupported = 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window
    
    if (!this.isSupported) {
      console.warn('[WebSpeechService] Web Speech API not supported in this browser')
    }
  }

  /**
   * Initialize speech recognition
   */
  private initializeRecognition(): void {
    if (!this.isSupported) return

    // Use webkit prefix for Chrome/Safari, standard for others
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
    this.recognition = new SpeechRecognition()

    // Configure recognition
    this.recognition.continuous = this.options.continuous!
    this.recognition.interimResults = this.options.interimResults!
    this.recognition.lang = this.options.language!
    this.recognition.maxAlternatives = this.options.maxAlternatives!

    // Set up event handlers
    this.setupEventHandlers()
  }

  /**
   * Set up speech recognition event handlers
   */
  private setupEventHandlers(): void {
    if (!this.recognition) return

    this.recognition.onstart = () => {
      console.log('[WebSpeechService] Speech recognition started')
      this.isListening = true
      this.emit('start')
    }

    this.recognition.onend = () => {
      console.log('[WebSpeechService] Speech recognition ended')
      this.isListening = false
      this.emit('end')
    }

    this.recognition.onresult = (event: SpeechRecognitionEvent) => {
      console.log('[WebSpeechService] Speech recognition result:', event)
      
      let finalTranscript = ''
      let interimTranscript = ''

      // Process all results
      for (let i = event.resultIndex; i < event.results.length; i++) {
        const result = event.results[i]
        const transcript = result[0].transcript

        if (result.isFinal) {
          finalTranscript += transcript
        } else {
          interimTranscript += transcript
        }
      }

      // Emit result with both final and interim transcripts
      const transcriptionResult: TranscriptionResult = {
        transcript: finalTranscript || interimTranscript,
        confidence: event.results[event.results.length - 1]?.[0]?.confidence || 0,
        isFinal: finalTranscript.length > 0
      }

      this.emit('result', transcriptionResult)
    }

    this.recognition.onerror = (event: SpeechRecognitionErrorEvent) => {
      console.error('[WebSpeechService] Speech recognition error:', event.error)
      this.emit('error', {
        error: event.error,
        message: event.message || 'Speech recognition error occurred'
      })
    }

    this.recognition.onspeechstart = () => {
      console.log('[WebSpeechService] Speech detected')
      this.emit('speechstart')
    }

    this.recognition.onspeechend = () => {
      console.log('[WebSpeechService] Speech ended')
      this.emit('speechend')
    }

    this.recognition.onsoundstart = () => {
      console.log('[WebSpeechService] Sound detected')
      this.emit('soundstart')
    }

    this.recognition.onsoundend = () => {
      console.log('[WebSpeechService] Sound ended')
      this.emit('soundend')
    }
  }

  /**
   * Start speech recognition
   */
  public start(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      if (!this.isSupported) {
        reject(new Error('Web Speech API not supported'))
        return
      }

      if (!this.recognition) {
        reject(new Error('Speech recognition not initialized'))
        return
      }

      if (this.isListening) {
        resolve(true)
        return
      }

      try {
        this.recognition.start()
        resolve(true)
      } catch (error) {
        console.error('[WebSpeechService] Error starting recognition:', error)
        reject(error)
      }
    })
  }

  /**
   * Stop speech recognition
   */
  public stop(): void {
    if (this.recognition && this.isListening) {
      this.recognition.stop()
    }
  }

  /**
   * Abort speech recognition
   */
  public abort(): void {
    if (this.recognition && this.isListening) {
      this.recognition.abort()
    }
  }

  /**
   * Check if service is supported
   */
  public isWebSpeechSupported(): boolean {
    return this.isSupported
  }

  /**
   * Check if currently listening
   */
  public isCurrentlyListening(): boolean {
    return this.isListening
  }

  /**
   * Add event listener
   */
  public on(event: WebSpeechEventType, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event)!.push(callback)
  }

  /**
   * Remove event listener
   */
  public off(event: WebSpeechEventType, callback: Function): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      const index = listeners.indexOf(callback)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  /**
   * Emit event to listeners
   */
  private emit(event: WebSpeechEventType, data?: any): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      listeners.forEach(callback => callback(data))
    }
  }

  /**
   * Update language setting
   */
  public setLanguage(language: string): void {
    this.options.language = language
    if (this.recognition) {
      this.recognition.lang = language
    }
  }

  /**
   * Get current language
   */
  public getLanguage(): string {
    return this.options.language || 'en-US'
  }

  /**
   * Cleanup resources
   */
  public destroy(): void {
    this.stop()
    this.eventListeners.clear()
    this.recognition = null
  }
}

// Extend Window interface for TypeScript
declare global {
  interface Window {
    SpeechRecognition: typeof SpeechRecognition
    webkitSpeechRecognition: typeof SpeechRecognition
  }
}

export default WebSpeechService
export type { WebSpeechOptions, TranscriptionResult, WebSpeechEventType }
